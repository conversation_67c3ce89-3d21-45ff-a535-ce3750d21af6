import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/requests/order/order_detail_request.dart';

part 'order_request_model.freezed.dart';
part 'order_request_model.g.dart';

@Freezed(fromJson: false, toJson: true)
class OrderRequestModel with _$OrderRequestModel {
  const factory OrderRequestModel({
    required String restaurantId,
    required String tableNumber,
    required List<OrderDetailRequest> details,
    String? couponId,
    @Default(false) bool isOrderAndPay,
  }) = _OrderRequestModel;
}