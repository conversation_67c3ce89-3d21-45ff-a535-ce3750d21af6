import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/models/loyalty/loyalty_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class CouponSelectionScreen extends StatefulWidget {
  final String restaurantId;
  final PrecalculatedOrderRequest? preOrder;

  const CouponSelectionScreen({required this.restaurantId, required this.preOrder, super.key});

  @override
  State<CouponSelectionScreen> createState() => _CouponSelectionScreenState();
}

class _CouponSelectionScreenState extends State<CouponSelectionScreen> {
  late final OrderFlowBloc _orderFlowBloc;
  late final CouponsBloc _couponsBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;
  final GlobalKey _usedCouponsKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _orderFlowBloc = context.read<OrderFlowBloc>();
    _couponsBloc = context.read<CouponsBloc>();
    if (widget.preOrder != null) {
      _couponsBloc.add(CouponsEvent.fetchAvailableCoupons(widget.preOrder!, widget.restaurantId));
    }
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
  }

  /// Проверяет, нужно ли показать туториал для использованных купонов
  /// Туториал показывается когда:
  /// 1. У пользователя есть использованные купоны, которые можно обновить (canReactivate = true)
  /// 2. У пользователя достаточно баллов для обновления хотя бы одного купона
  bool _shouldShowUsedCouponsTutorial(CouponsState couponsState, int userBalance) {
    final usedCoupons = couponsState.usedCoupons;
    if (usedCoupons.isEmpty) return false;

    // Проверяем есть ли купоны, которые можно обновить за имеющиеся баллы
    return usedCoupons.any((coupon) => userBalance >= coupon.price);
  }

  /// Показывает туториал для использованных купонов если это необходимо
  void _showUsedCouponsTutorialIfNeeded(CouponsState couponsState, int userBalance) {
    if (_shouldShowUsedCouponsTutorial(couponsState, userBalance)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Tutorials.showUsedCouponsTutorial(
            context,
            usedCouponsKey: _usedCouponsKey,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: BlocBuilder<CouponsBloc, CouponsState>(
            bloc: _couponsBloc,
            builder: (BuildContext context, CouponsState state) {
              return switch (state.couponsStatus) {
                CouponsStatus.idle => const SizedBox(),
                CouponsStatus.loading => const Center(
                    child: CircularProgressIndicator(
                      color: MColors.onyxShadow,
                      strokeWidth: 1.0,
                    ),
                  ),
                CouponsStatus.success => state.availableCouponsByMenu.isEmpty && state.usedCoupons.isEmpty
                    ? Center(
                        child: Text('No available coupons', style: Theme.of(context).textTheme.bodyLarge),
                      )
                    : Builder(
                        builder: (context) {
                          final userBalance = _userLoyaltyBloc.state.loyalty?.getBalanceByRestaurant(widget.restaurantId) ?? 0;

                          // Показываем туториал если есть купоны для обновления
                          _showUsedCouponsTutorialIfNeeded(state, userBalance);

                          return SingleChildScrollView(
                            child: CouponList(
                              balance: userBalance,
                              availableCoupons: state.availableCouponsByMenuUsable,
                              usedCoupons: state.usedCoupons,
                              nonAvailableCoupons: state.nonAvailableCouponsByMenuUsable,
                              selectedCouponId: state.selectedCouponId,
                              usedCouponsKey: _usedCouponsKey,
                              onCouponSelected: (couponId) {
                                _couponsBloc.add(CouponsEvent.selectCoupon(couponId));
                                final dishes = [
                                  ..._orderFlowBloc.state.cart,
                                  ..._orderFlowBloc.state.order,
                                ];
                                final specials = [
                                  ..._orderFlowBloc.state.specialCart,
                                  ..._orderFlowBloc.state.orderSpecialCart,
                                ];
                                _orderFlowBloc.add(OrderFlowEvent.precalculateOrder(
                                    restaurantId: widget.restaurantId, couponId: couponId, dishes: dishes, specials: specials));

                                try {
                                  Platform.isAndroid ? HapticFeedback.vibrate() : HapticFeedback.heavyImpact();
                                } catch (e, s) {
                                  log('HapticFeedback.heavyImpact() error: $e, $s');
                                }
                              },
                              onCouponRefresh: (couponId) {
                                final coupon = state.usedCoupons.firstWhere((coupon) => coupon.coupon.id == couponId);
                                showConfirmCouponDialog(
                                  onSuccess: () {
                                    _couponsBloc.add(CouponsEvent.fetchAvailableCoupons(widget.preOrder!, widget.restaurantId));
                                  },
                                  context: context,
                                  restaurantId: widget.restaurantId,
                                  coupon: coupon,
                                  balance: userBalance,
                                );
                              },
                            ),
                          );
                        },
                      ),
                CouponsStatus.error => const SizedBox(),
              };
            },
          ),
        ),
        Container(
          width: displayWidth,
          constraints: const BoxConstraints(
            minHeight: 174.0,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 40.0),
          decoration: BoxDecoration(
            color: MColors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(MSizes.cardBorderRadius),
              topRight: Radius.circular(MSizes.cardBorderRadius),
            ),
            boxShadow: [
              BoxShadow(
                color: MColors.black.withOpacity(.1),
                blurRadius: 40.0,
                offset: const Offset(0, 0),
              ),
            ],
          ),
          child: Column(
            children: [
              BlocConsumer<OrderFlowBloc, OrderFlowState>(
                  bloc: _orderFlowBloc,
                  listener: (context, state) {
                    if (state.detachCouponStatus == DetachCouponStatus.success) {
                      _couponsBloc.add(const CouponsEvent.clearCoupon());
                      final dishes = [
                        ..._orderFlowBloc.state.cart,
                        ..._orderFlowBloc.state.order,
                      ];
                      final specials = [
                        ..._orderFlowBloc.state.specialCart,
                        ..._orderFlowBloc.state.orderSpecialCart,
                      ];
                      _orderFlowBloc.add(OrderFlowEvent.precalculateOrder(
                          restaurantId: widget.restaurantId, couponId: null, dishes: dishes, specials: specials));
                      _orderFlowBloc.add(const OrderFlowEvent.changeOrderFlowType(OrderFlowType.payment));
                    }

                    if (state.detachCouponStatus == DetachCouponStatus.error && state.detachCouponError != null) {
                      showErrorDialog(
                        context: context,
                        message: state.detachCouponError!,
                      );
                    }
                  },
                  builder: (context, state) {
                    return MElevatedButton(
                      isLoading: state.detachCouponStatus == DetachCouponStatus.loading,
                      loadingIndicatorColor: MColors.onyxShadow,
                      onPressed: () {
                        if (state.preOrder?.couponId != null) {
                          _orderFlowBloc.add(OrderFlowEvent.detachCoupon(orderId: state.preOrder!.id));
                        } else {
                          _couponsBloc.add(const CouponsEvent.clearCoupon());
                          final dishes = [
                            ..._orderFlowBloc.state.cart,
                            ..._orderFlowBloc.state.order,
                          ];
                          final specials = [
                            ..._orderFlowBloc.state.specialCart,
                            ..._orderFlowBloc.state.orderSpecialCart,
                          ];
                          _orderFlowBloc.add(OrderFlowEvent.precalculateOrder(
                              restaurantId: widget.restaurantId, couponId: null, dishes: dishes, specials: specials));
                          _orderFlowBloc.add(const OrderFlowEvent.changeOrderFlowType(OrderFlowType.payment));
                        }
                      },
                      background: MColors.white,
                      boxShadowColor: MColors.slateGrayMist.withOpacity(.25),
                      label: t.screens.invoice.doNotUseCoupon,
                      labelStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: MColors.slateGrayMist,
                          ),
                      suffix: Visibility(
                        visible: true,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: SvgPicture.asset(
                            MAssets.arrowRight,
                            colorFilter: const ColorFilter.mode(
                              MColors.slateGrayMist,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
              const SizedBox(height: 20.0),
              Column(
                children: [
                  MElevatedButton(
                    onPressed: () => _orderFlowBloc.add(const OrderFlowEvent.changeOrderFlowType(OrderFlowType.payment)),
                    label: t.screens.invoice.proceedToPayment,
                    suffix: Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: SvgPicture.asset(
                        MAssets.arrowRight,
                        colorFilter: const ColorFilter.mode(
                          MColors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
