import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/extensions/context_extensions.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/responses/tutorial/tutorial_states_response.dart';
import 'package:mutualz/src/presentation/home/<USER>/profile/settings_item.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/settings/screens/general_settings_screen.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';

class SettingsList extends StatelessWidget {
  final GlobalKey ordersButtonKey;

  const SettingsList({super.key, required this.ordersButtonKey});

  @override
  Widget build(BuildContext context) {
    final AuthBloc authBloc = context.read<AuthBloc>();
    final AppBloc appBloc = context.read<AppBloc>();
    final displayWidth = MediaQuery.of(context).size.width;
    final s = t.screens.profile.settings;
    return Padding(
      padding: EdgeInsets.only(top: 24.0, bottom: displayWidth * MSizes.kHeightBnB),
      child: Column(
        children: [
          SettingsItem(
            icon: MAssets.iconSettingsGeneral,
            label: s.general,
            onTap: () => context.pushNamed(MRoutes.generalSettings),
          ),
          SettingsItem(
            icon: MAssets.iconSettingsHelp,
            label: s.help,
            onTap: () => context.pushNamed(MRoutes.faq),
          ),
          SettingsItem(
            icon: MAssets.iconSettingsFeedback,
            label: s.feedback,
            onTap: () => context.pushNamed(MRoutes.feedback)
          ),
          TutorialShowcase(
                showcaseKey: ordersButtonKey,
                container: Container(
                  padding: EdgeInsets.only(right: context.width * 0.5, left: context.width * 0.07),
                  alignment: Alignment.centerLeft,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 16, bottom: 8),
                        child: Text(
                          t.tutorial.profile.orders,
                          style: const TextStyle(fontSize: 14),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Transform(
                        transform:Matrix4.identity()
                          ..translate(-26.0, 0.0, 0.0)
                          ..rotateZ(math.pi / 1.2),
                        alignment: Alignment.center,
                        child: SvgPicture.asset(MAssets.tutorialArrow1, height: 80),
                      ),
                    ],
                  ),
                ),
                targetPadding: const EdgeInsets.only(top: 12, left: 12, right: 12), // Corrects the padding of the target
                targetBorderRadius: BorderRadius.circular(12),
            child: SettingsItem(
              icon: MAssets.iconSettingsOrders,
              label: s.orders,
              margin: const EdgeInsets.only(bottom: 12),
              onTap: () => context.pushNamed(MRoutes.orders),
            ),
          ),
          Divider(
            color: MColors.onyxShadow.withOpacity(.1),
          ),
          if (kDebugMode)
            SettingsItem(
              icon: MAssets.tutorialArrow1,
              label: 'Reset Tutorial States',
              onTap: () {
                authBloc.add(AuthEvent.updateTutorialStates({
                  TutorialKey.mapTutorial.key: false,
                  TutorialKey.feedTutorial.key: false,
                  TutorialKey.gastroProfileTutorial.key: false,
                  TutorialKey.userAndSettingsTutorial.key: false,
                  TutorialKey.mapFilterTutorial.key: false,
                  TutorialKey.orderCartButtonTutorial.key: false,
                  TutorialKey.orderCartItemsTutorial.key: false,
                  TutorialKey.orderFirstLoyaltyPointsTutorial.key: false,
                  TutorialKey.orderPaymentOptionsTutorial.key: false,
                  TutorialKey.orderPointsUsageTutorial.key: false,
                  TutorialKey.orderTableNumberTutorial.key: false,
                  TutorialKey.orderUsedCouponsTutorial.key: false,
                  TutorialKey.orderCongratulationTutorial.key: false,
                  TutorialKey.loyaltyPointsTutorial.key: false,
                  TutorialKey.generalSettingsTutorial.key: false,
                }));
              },
              margin: const EdgeInsets.only(top: 12.0),
            ),
          SettingsItem(
            icon: MAssets.iconSettingsLogout,
            label: s.logout,
            onTap: () {
              appBloc.add(const AppEvent.setUserLoaded(value: false));
              appBloc.add(const AppEvent.setRestaurantsLoaded(false));
              authBloc.add(const AuthEvent.logout());
            },
            margin: const EdgeInsets.only(top: 12.0),
          ),
          const SizedBox(height: 24.0),
        ],
      ),
    );
  }
}
