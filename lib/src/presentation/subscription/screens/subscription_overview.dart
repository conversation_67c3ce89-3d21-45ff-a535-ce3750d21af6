import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/constants/loyalty_magic_numbers.dart';
import 'package:mutualz/src/domain/models/loyalty/loyalty_model.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/subscription/widgets/modal/subscription_modal_info.dart';
import 'package:mutualz/src/presentation/subscription/widgets/overview/overview_card.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/widgets/base/simple_back_app_bar.dart';

class SubscriptionOverview extends StatefulWidget {
  const SubscriptionOverview({super.key});

  @override
  State<SubscriptionOverview> createState() => _SubscriptionOverviewState();
}

class _SubscriptionOverviewState extends State<SubscriptionOverview> {
  late final UserLoyaltyBloc _userLoyaltyBloc;

  @override
  void initState() {
    super.initState();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();

    TutorialUtils.scheduleAfterBuild(this, () => Tutorials.showLoyaltyPointsTutorialPage(context), delay: Duration.zero);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserLoyaltyBloc, UserLoyaltyState>(
      bloc: _userLoyaltyBloc,
      builder: (context, state) {
        return Scaffold(
          backgroundColor: MColors.softCotton,
          appBar: SimpleBackAppBar(
            type: SimpleBackAppBarType.button,
            onBack: () => context.pop(),
          ),
          body: SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 16.0,
                left: 32.0,
                right: 32.0,
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                   Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            t.screens.subscription.title,
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          GestureDetector(
                            onTap: () {
                              context.pushNamed(MRoutes.loyaltyTutorial);
                            },
                            child: SvgPicture.asset(
                                MAssets.iconInfo,
                                width: 64,
                                height: 64,
                                colorFilter: const ColorFilter.mode(MColors.scarletEmber, BlendMode.srcIn),
                              ),
                          ),
                        ],
                      ),
                    // Text(
                    //   t.screens.subscription.description,
                    //   style: Theme.of(context).textTheme.bodyLarge,
                    // ),
                    const SizedBox(height: 40.0),
                    OverviewCard(
                      onTap: () {
                        showSubscriptionModelInfo(
                          context: context,
                          type: SubscriptionType.test,
                        );
                      },
                      svgIcon: MAssets.iconTest,
                      discount: '0',
                      iconBackground: MColors.azureSky,
                      shadowColor: MColors.azureSky,
                      label: t.screens.subscription.levels.test,
                    ),
                    OverviewCard(
                      onTap: () {
                        showSubscriptionModelInfo(
                          context: context,
                          type: SubscriptionType.member,
                          maxMemberPoints: loyaltyMemberPoints,
                          points: state.loyalty?.totalPoints ?? 0.0,
                          showPaymentPanel: true,
                        );
                      },
                      svgIcon: MAssets.iconMember,
                      discount: '5',
                      iconBackground: MColors.scarletEmber,
                      shadowColor: MColors.white,
                      label: t.screens.subscription.levels.member,
                    ),
                    OverviewCard(
                      onTap: () {
                        showSubscriptionModelInfo(
                          context: context,
                          type: SubscriptionType.silver,
                          userLoyalties: state.loyalty?.silverLoyalties ?? [],
                        );
                      },
                      svgIcon: MAssets.iconSilver,
                      discount: '10',
                      iconBackground: MColors.black,
                      label: t.screens.subscription.levels.silver,
                    ),
                    OverviewCard(
                      onTap: () {
                        showSubscriptionModelInfo(
                          context: context,
                          type: SubscriptionType.gold,
                          userLoyalties: state.loyalty?.goldLoyalties ?? [],
                        );
                      },
                      svgIcon: MAssets.iconGold,
                      discount: '15',
                      iconBackground: MColors.apricotSunset,
                      label: t.screens.subscription.levels.gold,
                      iconGradient: const LinearGradient(
                        colors: [
                          MColors.tangerineOrange,
                          MColors.vividYellow,
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                    Center(
                        child: Text(
                          t.tutorial.loyalty.terms,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: MColors.charcoalGray,
                              ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    );
  }
}
