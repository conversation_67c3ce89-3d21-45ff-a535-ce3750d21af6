// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderRequestModel {
  String get restaurantId => throw _privateConstructorUsedError;
  String get tableNumber => throw _privateConstructorUsedError;
  List<OrderDetailRequest> get details => throw _privateConstructorUsedError;
  String? get couponId => throw _privateConstructorUsedError;
  bool get isOrderAndPay => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderRequestModelCopyWith<OrderRequestModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderRequestModelCopyWith<$Res> {
  factory $OrderRequestModelCopyWith(
          OrderRequestModel value, $Res Function(OrderRequestModel) then) =
      _$OrderRequestModelCopyWithImpl<$Res, OrderRequestModel>;
  @useResult
  $Res call(
      {String restaurantId,
      String tableNumber,
      List<OrderDetailRequest> details,
      String? couponId,
      bool isOrderAndPay});
}

/// @nodoc
class _$OrderRequestModelCopyWithImpl<$Res, $Val extends OrderRequestModel>
    implements $OrderRequestModelCopyWith<$Res> {
  _$OrderRequestModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? tableNumber = null,
    Object? details = null,
    Object? couponId = freezed,
    Object? isOrderAndPay = null,
  }) {
    return _then(_value.copyWith(
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      tableNumber: null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
      details: null == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as List<OrderDetailRequest>,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      isOrderAndPay: null == isOrderAndPay
          ? _value.isOrderAndPay
          : isOrderAndPay // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderRequestModelImplCopyWith<$Res>
    implements $OrderRequestModelCopyWith<$Res> {
  factory _$$OrderRequestModelImplCopyWith(_$OrderRequestModelImpl value,
          $Res Function(_$OrderRequestModelImpl) then) =
      __$$OrderRequestModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String restaurantId,
      String tableNumber,
      List<OrderDetailRequest> details,
      String? couponId,
      bool isOrderAndPay});
}

/// @nodoc
class __$$OrderRequestModelImplCopyWithImpl<$Res>
    extends _$OrderRequestModelCopyWithImpl<$Res, _$OrderRequestModelImpl>
    implements _$$OrderRequestModelImplCopyWith<$Res> {
  __$$OrderRequestModelImplCopyWithImpl(_$OrderRequestModelImpl _value,
      $Res Function(_$OrderRequestModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? tableNumber = null,
    Object? details = null,
    Object? couponId = freezed,
    Object? isOrderAndPay = null,
  }) {
    return _then(_$OrderRequestModelImpl(
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      tableNumber: null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
      details: null == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as List<OrderDetailRequest>,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      isOrderAndPay: null == isOrderAndPay
          ? _value.isOrderAndPay
          : isOrderAndPay // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$OrderRequestModelImpl implements _OrderRequestModel {
  const _$OrderRequestModelImpl(
      {required this.restaurantId,
      required this.tableNumber,
      required final List<OrderDetailRequest> details,
      this.couponId,
      this.isOrderAndPay = false})
      : _details = details;

  @override
  final String restaurantId;
  @override
  final String tableNumber;
  final List<OrderDetailRequest> _details;
  @override
  List<OrderDetailRequest> get details {
    if (_details is EqualUnmodifiableListView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_details);
  }

  @override
  final String? couponId;
  @override
  @JsonKey()
  final bool isOrderAndPay;

  @override
  String toString() {
    return 'OrderRequestModel(restaurantId: $restaurantId, tableNumber: $tableNumber, details: $details, couponId: $couponId, isOrderAndPay: $isOrderAndPay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderRequestModelImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.tableNumber, tableNumber) ||
                other.tableNumber == tableNumber) &&
            const DeepCollectionEquality().equals(other._details, _details) &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId) &&
            (identical(other.isOrderAndPay, isOrderAndPay) ||
                other.isOrderAndPay == isOrderAndPay));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, restaurantId, tableNumber,
      const DeepCollectionEquality().hash(_details), couponId, isOrderAndPay);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderRequestModelImplCopyWith<_$OrderRequestModelImpl> get copyWith =>
      __$$OrderRequestModelImplCopyWithImpl<_$OrderRequestModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderRequestModelImplToJson(
      this,
    );
  }
}

abstract class _OrderRequestModel implements OrderRequestModel {
  const factory _OrderRequestModel(
      {required final String restaurantId,
      required final String tableNumber,
      required final List<OrderDetailRequest> details,
      final String? couponId,
      final bool isOrderAndPay}) = _$OrderRequestModelImpl;

  @override
  String get restaurantId;
  @override
  String get tableNumber;
  @override
  List<OrderDetailRequest> get details;
  @override
  String? get couponId;
  @override
  bool get isOrderAndPay;
  @override
  @JsonKey(ignore: true)
  _$$OrderRequestModelImplCopyWith<_$OrderRequestModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
