import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/extensions/context_extensions.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/settings/widgets/general/change_preferences_sheet.dart';
import 'package:mutualz/src/presentation/settings/widgets/general/reset_password_dialog.dart';
import 'package:mutualz/src/presentation/settings/widgets/general/settings_header.dart';
import 'package:mutualz/src/presentation/settings/widgets/general/simple_settings_item.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class GeneralSettingsScreen extends StatefulWidget {
  const GeneralSettingsScreen({super.key});

  @override
  State<GeneralSettingsScreen> createState() => _GeneralSettingsScreenState();
}

class _GeneralSettingsScreenState extends State<GeneralSettingsScreen> {
  late final DiscoverBloc _discoverBloc;
  late final SearchBloc _searchBloc;
  final GlobalKey _tutorialSettingsPreferencesButtonKey = GlobalKey();

  
  @override
  void initState() {
    super.initState();
    _discoverBloc = context.read<DiscoverBloc>();
    _searchBloc = context.read<SearchBloc>();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Tutorials.showGeneralSettingsTutorial(context, preferencesButtonKey: _tutorialSettingsPreferencesButtonKey);
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColors.white,
      appBar: const SimpleBackAppBar(
        type: SimpleBackAppBarType.button,
        backgroundColor: MColors.white
      ),
      body: Padding(
        padding: const EdgeInsets.only(
          top: 24.0,
          left: 32.0,
          right: 32.0,
        ),
        child: ListView(
          children: [
            SimpleSettingsItem(
              localAssetPath: MAssets.iconGeneralSettingsUser,
              label: t.screens.generalSettings.profile,
              onTap: () => context.pushNamed(MRoutes.userProfileSettings)
            ),
            SimpleSettingsItem(
              localAssetPath: MAssets.iconGeneralSettingsPassword,
              label: t.screens.generalSettings.password,
              onTap: () => showResetPasswordDialog(context: context),
            ),
            ListeningConditionalWrapper<bool>(
              listenable: Tutorials.isProfileTutorialActive,
              condition: (value) => value,
              wrapper: (child) => TutorialShowcase(
                showcaseKey: _tutorialSettingsPreferencesButtonKey,
                tooltipPosition: TooltipPosition.bottom,
                container: Container(
                  padding: EdgeInsets.only(
                    right: context.width * 0.5,
                    left: context.width * 0.1,
                  ),
                  child: Transform.translate(
                    offset: const Offset(0, -20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Transform(
                          transform: Matrix4.identity()..rotateZ(-math.pi / 3),
                          alignment: Alignment.center,
                          child: SvgPicture.asset(MAssets.tutorialArrow1, height: 80),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(right: 16, top: 8),
                          child: Text(
                            t.tutorial.profile.dietPreferences,
                            style: const TextStyle(fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                onTap: () => Tutorials.isProfileTutorialActive.value = false,
                targetBorderRadius: BorderRadius.circular(12),
                targetPadding: const EdgeInsets.only(top: 20, left: 20, right: 20),
                child: child,
              ),
              child: SimpleSettingsItem(
                localAssetPath: MAssets.iconGeneralSettingsPreferences,
                label: t.screens.generalSettings.preferences,
                onTap: () => showChangePreferencesSheet(context),
              ),
            ),
            // SimpleSettingsItem(
            //   localAssetPath: MAssets.iconGeneralSettingsPayment,
            //   label: t.screens.generalSettings.payment,
            //   onTap: () => context.pushNamed(MRoutes.paymentSettings),
            // ),
            SettingsHeader(
              title: 'Display',
            ),
            BlocBuilder<AppBloc, AppState>(
              bloc: context.read<AppBloc>(),
              builder: (context, state) {
                return MExpansionTile(
                  key: ValueKey(state.locale.translations.language),
                  margin: const EdgeInsets.only(bottom: 8.0),
                  title: state.locale.translations.language,
                  color: MColors.softCotton,
                  placeholder: 'Select language',
                  isMultiplyChoice: false,
                  items: AppLocale.values.map((e) => e.translations.language).toList(),
                  onSelected: (Set<String> value) {
                    ShowCaseWidget.of(context).dismiss();
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                    final locale = AppLocale.values.firstWhere(
                      (e) => e.translations.language == value.first,
                    );
                    context.read<AppBloc>().add(
                      AppEvent.changeLocale(locale),
                    );
                    _discoverBloc.add(const DiscoverEvent.fetchRestaurants());
                    _searchBloc.add(const SearchEvent.fetchRestaurants());
                    _searchBloc.add(const SearchEvent.fetchCollections());
                    });
                  },
                );
              }
            ),
            // SettingsHeader(
            //   title: 'Privacy',
            // ),
            // MExpansionTile(
            //   margin: const EdgeInsets.only(bottom: 8.0),
            //   title: 'Posts',
            //   items: ['Posts', 'Articles'],
            //   onSelected: (Set<String> value) {  },
            // ),
            // MExpansionTile(
            //   margin: const EdgeInsets.only(bottom: 8.0),
            //   title: 'Reviews',
            //   items: ['Reviews', 'Feedbacks'],
            //   onSelected: (Set<String> value) {  },
            // ),
            // SettingsHeader(
            //   title: 'Notifications',
            // ),
            // NotificationSettings(),
            SettingsHeader(
              title: 'Info',
            ),
            MPrivacy(),
          ],
        ),
      ),
    );
  }
}
