import 'package:flutter/material.dart';

class MarkdownText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextStyle? boldStyle;
  final TextAlign textAlign;
  const MarkdownText(this.text, {super.key, this.style, this.textAlign = TextAlign.start, this.boldStyle});

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: _parseMarkdown(text),
      textAlign: textAlign,
    );
  }

  TextSpan _parseMarkdown(String src) {
    final spans = <TextSpan>[];
    final regex = RegExp(r'\*\*(.+?)\*\*');
    final baseStyle = style ?? const TextStyle();
    
    final currentWeight = baseStyle.fontWeight ?? FontWeight.normal;
    final newWeightIndex = (currentWeight.index + 3).clamp(0, FontWeight.values.length - 1);

    final boldStyle = this.boldStyle ?? baseStyle.copyWith(
      fontWeight: FontWeight.values[newWeightIndex],
    );

    int start = 0;
    for (final m in regex.allMatches(src)) {
      if (m.start > start) {
        spans.add(TextSpan(
          text: src.substring(start, m.start),
          style: baseStyle,
        ));
      }
      spans.add(TextSpan(
        text: m[1],
        style: boldStyle,
      ));
      start = m.end;
    }

    if (start < src.length) {
      spans.add(TextSpan(
        text: src.substring(start),
        style: baseStyle,
      ));
    }
    return TextSpan(children: spans, style: baseStyle);
  }
}
