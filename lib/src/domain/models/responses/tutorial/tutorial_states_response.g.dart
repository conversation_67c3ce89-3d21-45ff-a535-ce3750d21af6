// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tutorial_states_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TutorialStatesResponseImpl _$$TutorialStatesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TutorialStatesResponseImpl(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: TutorialStatesData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$TutorialStatesResponseImplToJson(
        _$TutorialStatesResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

_$TutorialStatesDataImpl _$$TutorialStatesDataImplFromJson(
        Map<String, dynamic> json) =>
    _$TutorialStatesDataImpl(
      mapTutorial: json['mapTutorial'] == null
          ? false
          : _boolFromJson(json['mapTutorial']),
      feedTutorial: json['feedTutorial'] == null
          ? false
          : _boolFromJson(json['feedTutorial']),
      gastroProfileTutorial: json['gastroProfileTutorial'] == null
          ? false
          : _boolFromJson(json['gastroProfileTutorial']),
      userAndSettingsTutorial: json['userAndSettingsTutorial'] == null
          ? false
          : _boolFromJson(json['userAndSettingsTutorial']),
      mapFilterTutorial: json['mapFilterTutorial'] == null
          ? false
          : _boolFromJson(json['mapFilterTutorial']),
      orderCartButtonTutorial: json['orderCartButtonTutorial'] == null
          ? false
          : _boolFromJson(json['orderCartButtonTutorial']),
      orderCartItemsTutorial: json['orderCartItemsTutorial'] == null
          ? false
          : _boolFromJson(json['orderCartItemsTutorial']),
      orderFirstLoyaltyPointsTutorial:
          json['orderFirstLoyaltyPointsTutorial'] == null
              ? false
              : _boolFromJson(json['orderFirstLoyaltyPointsTutorial']),
      orderPaymentOptionsTutorial: json['orderPaymentOptionsTutorial'] == null
          ? false
          : _boolFromJson(json['orderPaymentOptionsTutorial']),
      orderPointsUsageTutorial: json['orderPointsUsageTutorial'] == null
          ? false
          : _boolFromJson(json['orderPointsUsageTutorial']),
      orderTableNumberTutorial: json['orderTableNumberTutorial'] == null
          ? false
          : _boolFromJson(json['orderTableNumberTutorial']),
      orderUsedCouponsTutorial: json['orderUsedCouponsTutorial'] == null
          ? false
          : _boolFromJson(json['orderUsedCouponsTutorial']),
      orderCongratulationTutorial: json['orderCongratulationTutorial'] == null
          ? false
          : _boolFromJson(json['orderCongratulationTutorial']),
      loyaltyPointsTutorial: json['loyaltyPointsTutorial'] == null
          ? false
          : _boolFromJson(json['loyaltyPointsTutorial']),
      generalSettingsTutorial: json['generalSettingsTutorial'] == null
          ? false
          : _boolFromJson(json['generalSettingsTutorial']),
    );

Map<String, dynamic> _$$TutorialStatesDataImplToJson(
        _$TutorialStatesDataImpl instance) =>
    <String, dynamic>{
      'mapTutorial': instance.mapTutorial,
      'feedTutorial': instance.feedTutorial,
      'gastroProfileTutorial': instance.gastroProfileTutorial,
      'userAndSettingsTutorial': instance.userAndSettingsTutorial,
      'mapFilterTutorial': instance.mapFilterTutorial,
      'orderCartButtonTutorial': instance.orderCartButtonTutorial,
      'orderCartItemsTutorial': instance.orderCartItemsTutorial,
      'orderFirstLoyaltyPointsTutorial':
          instance.orderFirstLoyaltyPointsTutorial,
      'orderPaymentOptionsTutorial': instance.orderPaymentOptionsTutorial,
      'orderPointsUsageTutorial': instance.orderPointsUsageTutorial,
      'orderTableNumberTutorial': instance.orderTableNumberTutorial,
      'orderUsedCouponsTutorial': instance.orderUsedCouponsTutorial,
      'orderCongratulationTutorial': instance.orderCongratulationTutorial,
      'loyaltyPointsTutorial': instance.loyaltyPointsTutorial,
      'generalSettingsTutorial': instance.generalSettingsTutorial,
    };
