import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/loyalty/loyalty_model.dart';
import 'package:mutualz/src/presentation/loyalty/widgets/loyalty_widget.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/progress_reward/earn_widget.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class ProgressRewardScreen extends StatefulWidget {
  final String restaurantId;
  final VoidCallback? onConfettiStart;
  final VoidCallback? onConfettiStop;
  final bool selfService;

  const ProgressRewardScreen(
      {required this.restaurantId,
      this.onConfettiStart,
      this.onConfettiStop,
      required this.selfService,
      super.key});

  @override
  State<ProgressRewardScreen> createState() => _ProgressRewardScreenState();
}

class _ProgressRewardScreenState extends State<ProgressRewardScreen> {
  late final OrderFlowBloc _orderFlowBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;

  final GlobalKey _orderCongratulationWidgetKey = GlobalKey();
  final GlobalKey _pointsContainerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _orderFlowBloc = context.read<OrderFlowBloc>();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
    _userLoyaltyBloc.add(const UserLoyaltyEvent.fetchUserLoyalty());
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onConfettiStart?.call();
    });

    TutorialUtils.scheduleAfterBuild(
      this,
      () => Tutorials.showOrderCongratulationTutorial(context, congratulationWidgetKey: _orderCongratulationWidgetKey),
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: displayWidth,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(
                top: 40.0,
                left: 24.0,
                right: 24.0,
              ),
              child: Column(
                  children: [
                  TutorialShowcase(
                    showcaseKey: _orderCongratulationWidgetKey,
                    tooltipPosition: TooltipPosition.bottom,
                    onTap: () => ShowCaseWidget.of(context).startShowCase([_pointsContainerKey]),
                    container: Container(
                      padding: EdgeInsets.only(right: 80, left: context.width * 0.2),
                      child: Transform.translate(
                        offset: const Offset(0, 30),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Transform(
                              transform: Matrix4.identity()
                              ..rotateZ(-pi / 2.05),
                              alignment: Alignment.center,
                              child: SvgPicture.asset(MAssets.tutorialArrow3, height: 40),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 54),
                              child: Text(
                                t.tutorial.orders.loyaltyPoints,
                                style: const TextStyle(fontSize: 14),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    targetPadding: const EdgeInsets.all(16),
                    child: LoyaltyWidget(restaurantId: widget.restaurantId),
                  ),
                  const SizedBox(height: 32.0),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          BlocBuilder<OrderFlowBloc, OrderFlowState>(
                            bloc: _orderFlowBloc,
                            builder: (context, state) {
                              return EarnWidget(
                                key: ValueKey(state.earnPoints?.earnedPoints),
                                earnedPoints: state.earnPoints?.earnedPoints.toInt() ?? 0,
                              );
                            },
                          ),
                          const SizedBox(height: 12.0),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Container(
                              constraints: const BoxConstraints(
                                maxWidth: 110.0,
                              ),
                              margin: const EdgeInsets.only(right: 10.0),
                              child: TutorialShowcase(
                                showcaseKey: _pointsContainerKey,
                                container: Padding(
                                  padding: EdgeInsets.only(left: context.width * 0.25, right: 65, bottom: 25),
                                  child: Row(
                                    children: [
                                      Flexible(child: Text(t.tutorial.orders.loyaltyPoints, textAlign: TextAlign.center,),),
                                      Transform(
                                        transform: Matrix4.identity()
                                        ..translate(0.0, 30.0, 0.0)
                                        ..rotateZ(pi / 1.8),
                                        alignment: Alignment.center,
                                        child: SvgPicture.asset(MAssets.tutorialArrow2, height: 60,),
                                      ),
                                    ],
                                  ),
                                ),
                                targetPadding: const EdgeInsets.all(8),
                                targetBorderRadius: BorderRadius.circular(12),
                                child: Center(
                                  child: BlocBuilder<UserLoyaltyBloc, UserLoyaltyState>(
                                    bloc: _userLoyaltyBloc,
                                    builder: (context, state) {
                                      return CouponIndicator(
                                        count: state.loyalty?.getBalanceByRestaurant(widget.restaurantId) ?? 0,
                                        margin: EdgeInsets.zero,
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 32.0),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: displayWidth,
            constraints: const BoxConstraints(
              minHeight: 106.0,
            ),
            padding:
                const EdgeInsets.symmetric(horizontal: 32.0, vertical: 40.0),
            decoration: BoxDecoration(
              color: MColors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(MSizes.cardBorderRadius),
                topRight: Radius.circular(MSizes.cardBorderRadius),
              ),
              boxShadow: [
                BoxShadow(
                  color: MColors.black.withOpacity(.1),
                  blurRadius: 40.0,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: MElevatedButton(
              onPressed: () {
                widget.onConfettiStop?.call();

                if (widget.selfService) {
                  context.pop(true);
                } else {
                  _orderFlowBloc.add(const OrderFlowEvent.changeOrderFlowType(
                      OrderFlowType.review));
                }
              },
              label: t.buttons.savePoints,
            ),
          ),
        ],
      ),
    );
  }
}
