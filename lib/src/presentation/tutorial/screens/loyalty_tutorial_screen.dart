import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/loyalty_tutorial_page_five.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/loyalty_tutorial_page_four.dart';
import 'package:mutualz/src/presentation/widgets/buttons/m_elevated_button.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/loyalty_tutorial_page_one.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/loyalty_tutorial_page_two.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/loyalty_tutorial_page_three.dart';

class LoyaltyTutorialScreen extends StatefulWidget {
  const LoyaltyTutorialScreen({super.key});

  @override
  State<LoyaltyTutorialScreen> createState() => _LoyaltyTutorialScreenState();
}

class _LoyaltyTutorialScreenState extends State<LoyaltyTutorialScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 5; // TODO: SUBSCRIPTION

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Last page reached, go back to where it was opened
      context.pop();
    }
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  String _getButtonText(BuildContext context) {
    switch (_currentPage) {
      // TODO: SUBSCRIPTION
      // case 5:
      // return t.tutorial.loyalty.buttons.subscribe;
      default:
        return t.tutorial.loyalty.buttons.next;
    }
  }

  Widget _buildTutorialPage(int pageIndex) {
    switch (pageIndex) {
      case 0:
        return const LoyaltyTutorialPageOne();
      case 1:
        return const LoyaltyTutorialPageTwo();
      case 2:
        return const LoyaltyTutorialPageThree();
      case 3:
        return const LoyaltyTutorialPageFour();
      case 4:
        return const LoyaltyTutorialPageFive();
      // TODO: SUBSCRIPTION
      //case 5:
      //  return const LoyaltyTutorialPageSix();
      default:
        return const LoyaltyTutorialPageOne();
    }
  }

  bool? _needBottomText() {
    if (_currentPage >= 1) {
      return true;
    }

    return null;
  }

  bool? _isLastPage() {
    // // TODO: SUBSCRIPTION
    // if (_currentPage == _totalPages - 1) {
    //   return true;
    // }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColors.softCotton,
      body: SafeArea(
        bottom: false,
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _totalPages,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return LayoutBuilder(
                    builder: (context, constraints) {
                      return SingleChildScrollView(
                        physics: const ClampingScrollPhysics(),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: constraints.maxHeight,
                          ),
                          child: IntrinsicHeight(
                            child: _buildTutorialPage(index),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            Container(
              color: MColors.softCotton,
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: _isLastPage() == true
                          ? Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: MElevatedButton(
                                  onPressed: () => context.pop(),
                                  label: t.tutorial.loyalty.buttons.later,
                                  width: double.infinity,
                                  background: MColors.white,
                                  textColor: MColors.onyxShadow,
                                  boxShadowColor: MColors.onyxShadow.withAlpha(30)),
                            )
                          : const SizedBox.shrink(key: ValueKey('empty_last_page'))),
                  MElevatedButton(
                    onPressed: _nextPage,
                    label: _getButtonText(context),
                    width: double.infinity,
                  ),
                  SizedBox(
                    height: 40,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: _needBottomText() != null
                          ? Padding(
                              key: const ValueKey('bottom_text'),
                              padding: const EdgeInsets.only(bottom: 18),
                              child: Text(
                                t.tutorial.loyalty.terms,
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: MColors.charcoalGray,
                                    ),
                              ),
                            )
                          : const SizedBox.shrink(key: ValueKey('empty')),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
