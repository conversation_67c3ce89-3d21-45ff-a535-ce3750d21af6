import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/utils/color_utils.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class CouponList extends StatefulWidget {
  final List<RestaurantCoupon> availableCoupons;
  final List<RestaurantCoupon> usedCoupons;
  final List<RestaurantCoupon> nonAvailableCoupons;
  final bool withInnerIndicator;
  final EdgeInsets padding;
  final String? selectedCouponId;
  final Function(String)? onCouponSelected;
  final Function(String)? onCouponRefresh;
  final int balance;
  final bool showUsedCouponsCounter;
  final GlobalKey? usedCouponsKey;

  const CouponList(
      {this.onCouponSelected,
      this.onCouponRefresh,
      this.selectedCouponId,
      this.availableCoupons = const [],
      this.usedCoupons = const [],
      this.nonAvailableCoupons = const [],
      this.withInnerIndicator = false,
      this.padding = const EdgeInsets.only(
        left: 32.0,
        right: 32.0,
        top: 30.0,
      ),
      this.balance = 0,
      this.showUsedCouponsCounter = true,
      this.usedCouponsKey,
      super.key});

  @override
  State<CouponList> createState() => _CouponListState();
}

class _CouponListState extends State<CouponList> {
  @override
  Widget build(BuildContext context) {
    print('Available coupons: ${widget.availableCoupons.length}');
    return Padding(
      padding: widget.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: widget.availableCoupons.isNotEmpty,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Text(t.coupons.appliable, style: Theme.of(context).textTheme.headlineSmall),
                    ),
                    Visibility(
                      visible: widget.withInnerIndicator,
                      child: CouponIndicator(
                        count: widget.balance.toInt(),
                        margin: const EdgeInsets.only(right: 0.0),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 18.0),
                Column(
                  children: widget.availableCoupons.asMap().entries.map((entry) {
                    final coupon = entry.value.coupon;
                    return Coupon(
                      color: ColorUtils.hexToColor(coupon.color),
                      percentage: coupon.percentage.toStringAsFixed(0),
                      name: coupon.name,
                      type: coupon.type,
                      itemsFree: coupon.itemsFree,
                      itemsToBuy: coupon.itemsToBuy,
                      colorizePercent: .6,
                      isActive: entry.value.couponId == widget.selectedCouponId,
                      onTap: () => widget.onCouponSelected?.call(coupon.id),
                      from: widget.balance.toDouble(),
                      to: entry.value.price.toDouble(),
                      // isUsed: coupon.,
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
          Visibility(
            visible: widget.usedCoupons.isNotEmpty,
            child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(t.coupons.canBeReactivated, style: Theme.of(context).textTheme.headlineSmall),
                      Visibility(
                        visible: widget.withInnerIndicator && widget.showUsedCouponsCounter,
                        child: CouponIndicator(
                          count: widget.usedCoupons.length,
                          margin: EdgeInsets.only(right: 0.0),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 18.0),
                  Column(
                    children: widget.usedCoupons.asMap().entries.map((entry) {
                      final coupon = entry.value.coupon;
                      return Coupon(
                        color: ColorUtils.hexToColor(coupon.color),
                        percentage: coupon.percentage.toStringAsFixed(0),
                        name: coupon.name,
                        type: coupon.type,
                        itemsFree: coupon.itemsFree,
                        itemsToBuy: coupon.itemsToBuy,
                        colorizePercent: .6,
                        isActive: false,
                        isUsed: true,
                        from: widget.balance.toDouble(),
                        to: entry.value.price.toDouble(),
                        onTap: () => widget.onCouponRefresh?.call(coupon.id),
                      );
                    }).toList(),
                  ),
                ],
            ),
          ),
          Visibility(
            visible: widget.nonAvailableCoupons.isNotEmpty,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(t.coupons.nonAppliable, style: Theme.of(context).textTheme.headlineSmall),
                    Visibility(
                      visible: widget.withInnerIndicator && widget.showUsedCouponsCounter,
                      child: CouponIndicator(
                        count: widget.nonAvailableCoupons.length,
                        margin: const EdgeInsets.only(right: 0.0),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 18.0),
                Column(
                  children: widget.nonAvailableCoupons.asMap().entries.map((entry) {
                    final coupon = entry.value.coupon;
                    return Coupon(
                      color: ColorUtils.hexToColor(coupon.color),
                      percentage: coupon.percentage.toStringAsFixed(0),
                      name: coupon.name,
                      type: coupon.type,
                      itemsFree: coupon.itemsFree,
                      itemsToBuy: coupon.itemsToBuy,
                      colorizePercent: .6,
                      isActive: false,
                      isUsed: true,
                      from: widget.balance.toDouble(),
                      to: entry.value.price.toDouble(),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
