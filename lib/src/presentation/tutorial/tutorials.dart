import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../domain/blocs/auth/auth_bloc.dart';
import '../../domain/models/responses/tutorial/tutorial_states_response.dart';

/// Tutorials list:
/// 1. Map QR Code Tutorial / mapTutorial
/// 2. Map Filter Tutorial / mapFilterTutorial
/// 3. Feed Tutorial / feedTutorial
/// 4. Restaurant Tutorial / gastroProfileTutorial
/// 5. Profile Tutorial Part 1 + Profile Tutorial Part 2 / userAndSettingsTutorial
/// 6. Order Table Number Tutorial / orderTableNumberTutorial
/// 7. Order Cart Button Tutorial / orderCartButtonTutorial
/// 8. Order Cart Items Tutorial / orderCartItemsTutorial
/// 9. Order Payment Options Tutorial / orderPaymentOptionsTutorial
/// 10. Order First Loyalty Points Tutorial / orderFirstLoyaltyPointsTutorial / t.orders.loyaltyPoints, page 17
/// 11. Order Used Coupons Tutorial ???? / orderUsedCouponsTutorial / t.orders.usedCoupons, page 16
/// 12. Order Points Usage Tutorial ???? / orderPointsUsageTutorial / t.orders.pointsUsage (NOT USED)
///
/// 13. Order congratulation tutorial / orderCongratulationTutorial
/// 14. Settings Tutorial Part 2 / generalSettingsTutorial
/// 15. Loyality Points View Tutorial / loyaltyPointsTutorial (In subscription page)
class Tutorials {
  static final ValueNotifier<bool> mapQRCodeTutorialActive = ValueNotifier<bool>(false);
  static final ValueNotifier<bool> mapFilterTutorialActive = ValueNotifier<bool>(false);

  static final ValueNotifier<bool> feedTutorialAddButtonHighlighted = ValueNotifier<bool>(false);

  static final ValueNotifier<int> restaurantTutorialStep = ValueNotifier<int>(-1);
  static final ValueNotifier<bool> isProfileTutorialActive = ValueNotifier<bool>(false);

  static AuthBloc _getAuthBloc(BuildContext context) => context.read<AuthBloc>();

  static bool _shouldShowTutorial(BuildContext context, TutorialKey tutorialKey) {
    final authBloc = _getAuthBloc(context);
    final tutorialStates = authBloc.state.tutorialStates;

    if (tutorialStates == null) return false;

    switch (tutorialKey) {
      case TutorialKey.mapTutorial:
        return !tutorialStates.mapTutorial;
      case TutorialKey.feedTutorial:
        return !tutorialStates.feedTutorial;
      case TutorialKey.gastroProfileTutorial:
        return !tutorialStates.gastroProfileTutorial;
      case TutorialKey.userAndSettingsTutorial:
        return !tutorialStates.userAndSettingsTutorial;
      case TutorialKey.mapFilterTutorial:
        return !tutorialStates.mapFilterTutorial;
      case TutorialKey.orderCartButtonTutorial:
        return !tutorialStates.orderCartButtonTutorial;
      case TutorialKey.orderCartItemsTutorial:
        return !tutorialStates.orderCartItemsTutorial;
      case TutorialKey.orderFirstLoyaltyPointsTutorial:
        return !tutorialStates.orderFirstLoyaltyPointsTutorial;
      case TutorialKey.orderPaymentOptionsTutorial:
        return !tutorialStates.orderPaymentOptionsTutorial;
      case TutorialKey.orderPointsUsageTutorial:
        return !tutorialStates.orderPointsUsageTutorial;
      case TutorialKey.orderTableNumberTutorial:
        return !tutorialStates.orderTableNumberTutorial;
      case TutorialKey.orderUsedCouponsTutorial:
        return !tutorialStates.orderUsedCouponsTutorial;
      case TutorialKey.orderCongratulationTutorial:
        return !tutorialStates.orderCongratulationTutorial;
      case TutorialKey.loyaltyPointsTutorial:
        return !tutorialStates.loyaltyPointsTutorial;
      case TutorialKey.generalSettingsTutorial:
        return !tutorialStates.generalSettingsTutorial;
    }
  }

  static void _markTutorialCompleted(BuildContext context, TutorialKey tutorialKey) =>
      _getAuthBloc(context).add(AuthEvent.updateTutorialStates({tutorialKey.key: true}));

  static void _showTutorialIfNeeded(
    BuildContext context,
    TutorialKey tutorialKey,
    VoidCallback showAction,
  ) {
    if (!_shouldShowTutorial(context, tutorialKey)) return;

    showAction();
    _markTutorialCompleted(context, tutorialKey);
  }

  static void showMapQRCodeTutorial(
    BuildContext context, {
    required GlobalKey scannerButtonKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.mapTutorial,
        () {
          mapQRCodeTutorialActive.value = true;
          ShowCaseWidget.of(context).startShowCase([scannerButtonKey]);
        },
      );

  static void showMapFilterTutorial(
    BuildContext context, {
    required GlobalKey dragHandleKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.mapFilterTutorial,
        () {
          mapFilterTutorialActive.value = true;
          ShowCaseWidget.of(context).startShowCase([dragHandleKey]);
        },
      );

  static void showFeedTutorial(
    BuildContext context, {
    required GlobalKey addButtonKey,
    required GlobalKey searchButtonKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.feedTutorial,
        () {
          feedTutorialAddButtonHighlighted.value = true;
          ShowCaseWidget.of(context).startShowCase([addButtonKey, searchButtonKey]);
        },
      );

  static void showRestaurantTutorial(
    BuildContext context, {
    required GlobalKey mainInfoKey,
    required GlobalKey likeButtonKey,
    required GlobalKey dietPreferencesKey,
    required GlobalKey dietPreferences2Key,
    required GlobalKey dealsKey,
    required GlobalKey menuKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.gastroProfileTutorial,
        () {
          restaurantTutorialStep.value = 0;
          ShowCaseWidget.of(context)
              .startShowCase([mainInfoKey, likeButtonKey, dietPreferencesKey, dietPreferences2Key, dealsKey, menuKey]);
        },
      );

  static void showProfileTutorialPart1(
    BuildContext context, {
    required GlobalKey subscriptionsButtonKey,
    required GlobalKey ordersButtonKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.userAndSettingsTutorial,
        () {
          ShowCaseWidget.of(context).startShowCase([subscriptionsButtonKey, ordersButtonKey]);
        },
      );

  static void showGeneralSettingsTutorial(
    BuildContext context, {
    required GlobalKey preferencesButtonKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.generalSettingsTutorial,
        () {
          isProfileTutorialActive.value = true;
          ShowCaseWidget.of(context).startShowCase([preferencesButtonKey]);
        },
      );

  static void showOrderTableNumberTutorial(
    BuildContext context, {
    required GlobalKey numberModalKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.orderTableNumberTutorial,
        () => ShowCaseWidget.of(context).startShowCase([numberModalKey]),
      );

  static void showOrderCartTutorial(
    BuildContext context, {
    required GlobalKey cartButtonKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.orderCartButtonTutorial,
        () => ShowCaseWidget.of(context).startShowCase([cartButtonKey]),
      );

  static void showOrderCartInsideTutorial(
    BuildContext context, {
    required GlobalKey itemKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.orderCartItemsTutorial,
        () => ShowCaseWidget.of(context).startShowCase([itemKey]),
      );

  static void showOrderCartOrderPaymentTutorial(
    BuildContext context, {
    required GlobalKey buttonsContainerKey,
  }) =>
      _showTutorialIfNeeded(
        context,
        TutorialKey.orderPaymentOptionsTutorial,
        () => ShowCaseWidget.of(context).startShowCase([buttonsContainerKey]),
      );

  static void showLoyaltyPointsTutorialPage(BuildContext context) => _showTutorialIfNeeded(
        context,
        TutorialKey.loyaltyPointsTutorial,
        () => context.pushNamed(MRoutes.loyaltyTutorial),
      );

  static void showOrderCongratulationTutorial(BuildContext context, {required GlobalKey congratulationWidgetKey}) => _showTutorialIfNeeded(
        context,
        TutorialKey.orderCongratulationTutorial,
        () => ShowCaseWidget.of(context).startShowCase([congratulationWidgetKey]),
      );

  static void showUsedCouponsTutorial(
    BuildContext context, {
    required GlobalKey usedCouponsKey,
  }) =>
      /*_showTutorialIfNeeded(
        context,
        TutorialKey.orderUsedCouponsTutorial,
        () => */
      ShowCaseWidget.of(context).startShowCase([usedCouponsKey] //),
          );
}
