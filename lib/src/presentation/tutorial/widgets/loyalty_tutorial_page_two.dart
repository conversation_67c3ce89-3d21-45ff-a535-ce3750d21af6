import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/subscription/widgets/overview/overview_icon.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/texts/markdown_text.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class LoyaltyTutorialPageTwo extends StatelessWidget {
  const LoyaltyTutorialPageTwo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        Text(
          '${t.tutorial.loyalty.level.toUpperCase()} 0',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: MColors.onyxShadow),
        ),
        const SizedBox(height: 40),
        PointProgress.testToMember(
          from: 50,
          to: 100,
          width: context.width * 0.8,
          showBottomText: false,
        ),
        const SizedBox(height: 30),
        const OverviewIcon(
          svgIcon: MAssets.iconTest,
          background: MColors.azureSky,
          shadowColor: MColors.azureSky,
          iconSize: 80,
          width: 120,
          height: 120,
          borderRadius: 32,
        ),
        const SizedBox(height: 30),
        Text(
          t.loyalty.test.toUpperCase(),
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: MColors.onyxShadow),
        ),
        const SizedBox(height: 20),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.18),
          child: MarkdownText(
            t.tutorial.loyalty.test,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: MColors.charcoalGray,
                  height: 1.5,
                ),
          ),
        ),
      ],
    );
  }
}
